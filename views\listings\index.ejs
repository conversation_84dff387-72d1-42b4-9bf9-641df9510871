<% layout("/layouts/boilerplate") %>
    <!-- <h3>All Listings</h3> -->
     <style>
        #filters{
            /* margin-top: 1rem; */
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter{
            text-align: center;
            margin: 1.4rem;
            opacity: 0.7;
        }
        .filter:hover{
            opacity: 1;
            cursor: pointer;

        }
        .filter p{
            font-size: 0.8rem;
        }
        .tax-info{
            display: none;
        }
        .tax-toggle{
            border:1px solid black;
            border-radius: 1rem;
            height: 4.5rem;
            padding: 1rem;
            margin-left: 6rem;
            display: flex;
            align-items: center;

        }
     </style>
     <div id="filters">
        <div class="filter">
            <div>
                <i class="fa-solid fa-fire"></i>
            </div>
            <p>Trending</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-bed"></i>
            </div>
            <p>Rooms</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-city"></i>
            </div>
            <p>Iconic Cities</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-person-swimming"></i>
            </div>
            <p>Swimming Pools</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-umbrella-beach"></i>
            </div>
            <p>Beach</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-landmark-flag"></i>
            </div>
            <p>Farms</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-bowl-food"></i>
            </div>
            <p>Dishes</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-car"></i>
            </div>
            <p>Big Parking</p>
        </div>
        <div class="filter">
            <div>
                <i class="fa-solid fa-mountain"></i>
            </div>
            <p>Views</p>
        </div>

        <div class="filter">
            <div>
                <i class="fa-solid fa-water"></i>
            </div>
            <p>Oceans</p>
        </div>
        <div class="tax-toggle">
            <div class="form-check-reverse form-switch">
                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                <label class="form-check-label" for="flexSwitchCheckDefault">Display total after taxes</label>
              </div>
        </div>
     </div>

     <!-- Search Results Information -->
     <% if (searchQuery) { %>
        <div class="search-results-info">
            <h5><i class="fas fa-search"></i> Search Results</h5>
            <p>
                <% if (allListings.length > 0) { %>
                    Found <%= allListings.length %> listing<%= allListings.length !== 1 ? 's' : '' %> for "<strong><%= searchQuery %></strong>"
                <% } else { %>
                    No listings found for "<strong><%= searchQuery %></strong>"
                <% } %>
            </p>
            <a href="/listings" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times"></i> Clear Search
            </a>
        </div>
     <% } %>

    <% if (allListings.length === 0 && searchQuery) { %>
        <!-- No Results Message -->
        <div class="no-results">
            <i class="fas fa-search-minus"></i>
            <h3>No listings found</h3>
            <p>We couldn't find any listings matching "<strong><%= searchQuery %></strong>"</p>
            <p>Try searching with different keywords or <a href="/listings">browse all listings</a></p>
        </div>
    <% } else { %>
        <div class="row row-cols-lg-3 row-cols-md-2 row-cols-sm-1 mt-3">
            <% for (let listing of allListings) { %>
                <a href="/listings/<%=listing._id%>" class="listing-link">
                    <div class="card col">
                        <img src="<%= listing.image.url %>" class="card-img-top" alt="listing_image" style="height:20rem">
                        <div class="card-img-overlay"></div>
                        <div class="card-body">
                            <p class="card-text">
                                <b><%=listing.title%></b><br>
                                    <% if (listing.price !== undefined && listing.price !== null) { %>
                                      &#8377;<%= listing.price.toLocaleString("en-IN") %> <i class="tax-info">+18% GST</i> /night
                                    <% } else { %>
                                      Price not available
                                    <% } %>
                                    <br>
                                <br>
                            </p>
                        </div>
                    </div>
                </a>
            <% } %>
        </div>
    <% } %>


    <script>
        let taxSwitch=document.getElementById("flexSwitchCheckDefault");
        taxSwitch.addEventListener("click",()=>{
           let taxInfo=document.getElementsByClassName("tax-info");
        //    console.log(taxInfo);
        for(info of taxInfo){
            if(info.style.display!="inline"){
                info.style.display="inline";
            }
            else{
                info.style.display="none";
            }
        }
        })
    </script>

























