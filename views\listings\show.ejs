<% layout("/layouts/boilerplate") -%>
<style>
  /* Responsive styles for show page */
  @media (max-width: 768px) {
    .show-container {
      margin: 0 1rem;
    }

    .show-card {
      margin: 0;
    }

    .show-img {
      height: 250px;
    }

    .review-card {
      width: 100% !important;
      margin: 0 0 1rem 0 !important;
    }

    #map {
      width: 100% !important;
      height: 300px !important;
      margin: 1rem 0 !important;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .action-buttons a,
    .action-buttons button {
      width: 100%;
    }
  }

  @media (min-width: 769px) and (max-width: 992px) {
    .show-card {
      width: 80%;
      margin: 0 auto;
    }

    .review-card {
      width: 45% !important;
    }
  }
</style>

<script>
  let mapToken="<%=process.env.MAP_TOKEN%>"
  console.log(mapToken);
  const coordinates="<%-JSON.stringify(listing.geometry.coordinates)%>";
</script>

<div class="container show-container">
  <div class="row">
    <div class="col-12 col-md-8 offset-md-2">
      <h3 class="mb-3"><b><%= listing.title %></b></h3>
    </div>
  </div>

  <div class="row">
    <div class="col-12 col-md-8 offset-md-2">
      <div class="card show-card">
        <img src="<%= listing.image.url %>" class="card-img-top show-img" alt="listing_image">
        <div class="card-body">
          <p class="card-text"><i>Owned By: <%=listing.owner.username%></i></p>
          <p class="card-text"><b><%= listing.description %></b></p>
          <p class="card-text">
            <% if (listing.price !== undefined && listing.price !== null) { %>
              &#8377;<%= listing.price.toLocaleString("en-IN") %>/night
            <% } else { %>
              Price not available
            <% } %>
          </p>
          <p class="card-text"><b><%= listing.location %></b></p>
          <p class="card-text"><b><%= listing.country %></b></p>
        </div>
      </div>
    </div>
  </div>

  <% if(currUser && currUser._id.equals(listing.owner._id)) { %>
    <div class="row">
      <div class="col-12 col-md-8 offset-md-2">
        <div class="action-buttons mb-4">
          <a href="/listings/<%= listing._id %>/edit" id="Editlink" class="btn">Edit Listing</a>
          <form method="POST" action="/listings/<%= listing._id %>?_method=DELETE" style="display: inline;">
            <button id="Deletelink" class="btn">Delete Listing</button>
          </form>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-12 col-md-8 offset-md-2">
      <hr>

      <% if(currUser) { %>
        <h4 class="mb-4">Leave a Review</h4>
        <form action="/listings/<%= listing._id %>/reviews" method="POST" novalidate class="needs-validation">
          <div class="mb-3">
            <legend>Rating:</legend>
            <fieldset class="starability-coinFlip">
              <input type="radio" id="no-rate" class="input-no-rate" name="review[rating]" value="1" checked aria-label="No rating." />
              <input type="radio" id="first-rate1" name="review[rating]" value="1" />
              <label for="first-rate1" title="Terrible">1 star</label>
              <input type="radio" id="first-rate2" name="review[rating]" value="2" />
              <label for="first-rate2" title="Not good">2 stars</label>
              <input type="radio" id="first-rate3" name="review[rating]" value="3" />
              <label for="first-rate3" title="Average">3 stars</label>
              <input type="radio" id="first-rate4" name="review[rating]" value="4" />
              <label for="first-rate4" title="Very good">4 stars</label>
              <input type="radio" id="first-rate5" name="review[rating]" value="5" />
              <label for="first-rate5" title="Amazing">5 stars</label>
            </fieldset>
          </div>
          <div class="mb-3">
            <label for="comment" class="form-label">Comments</label>
            <textarea name="review[comment]" required id="comment" cols="30" rows="5" class="form-control"></textarea>
            <div class="invalid-feedback">Please add some comment for review!</div>
          </div>
          <button class="btn btn-outline-dark">Submit Review</button>
        </form>
      <% } else { %>
        <div class="alert alert-info">
          <p class="mb-0">Please <a href="/login">login</a> to leave a review.</p>
        </div>
      <% } %>
    </div>
  </div>

  <div class="row">
    <div class="col-12 col-md-8 offset-md-2">
      <h4 class="mb-4">All Reviews (<%= listing.reviews.length %>)</h4>

      <% if(listing.reviews.length === 0) { %>
        <div class="alert alert-secondary text-center">
          <i class="fas fa-comment-slash fa-2x mb-2"></i>
          <p class="mb-0">No reviews yet. Be the first to review this listing!</p>
        </div>
      <% } else { %>
        <div class="row">
          <% for(let review of listing.reviews) { %>
            <div class="col-12 col-lg-6 mb-3">
              <div class="card review-card h-100" id="review">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <% if(review.author) { %>
                        <h6 class="card-title mb-1">
                          <i class="fas fa-user-circle"></i> <%= review.author.username %>
                        </h6>
                      <% } else { %>
                        <h6 class="card-title mb-1">
                          <i class="fas fa-user-circle"></i> Anonymous User
                        </h6>
                      <% } %>
                      <p class="starability-result card-text mb-2" data-rating="<%= review.rating %>"></p>
                    </div>

                    <!-- Only show delete button to review author or listing owner -->
                    <% if(currUser && (
                        (review.author && currUser._id.equals(review.author._id)) ||
                        currUser._id.equals(listing.owner._id)
                    )) { %>
                      <form action="/listings/<%= listing._id %>/reviews/<%= review._id %>?_method=DELETE" method="POST" style="display: inline;">
                        <button class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this review?')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </form>
                    <% } %>
                  </div>

                  <p class="card-text"><%= review.comment %></p>
                </div>
              </div>
            </div>
          <% } %>
        </div>
      <% } %>
    </div>
  </div>

  <% if(listing.geometry.coordinates.length) { %>
    <div class="row">
      <div class="col-12 col-md-8 offset-md-2">
        <h3 class="mb-3">Where You Will Be</h3>
        <div id="map" class="rounded"></div>
      </div>
    </div>
  <% } %>
</div>

<script src="/javascript/map.js"></script>