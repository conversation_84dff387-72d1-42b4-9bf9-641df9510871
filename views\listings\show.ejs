<% layout("/layouts/boilerplate") -%>
<script>
  let mapToken="<%=process.env.MAP_TOKEN%>"
  console.log(mapToken);
  const coordinates="<%-JSON.stringify(listing.geometry.coordinates)%>";
</script>
<div class="row">
  <div class="col-8 offset-3">
    <h3><b><%= listing.title %></b></h3>
  </div>
  <div class="card col-6 offset-3">
    <img src="<%= listing.image.url %>" class="card-img-top show-img" alt="listing_image">
    <div class="card-body">
      <p class="card-text"><i>Owned By: <%=listing.owner.username%></i></p>
      <p class="card-text"><b><%= listing.description %></b></p>
      <p class="card-text">
        <% if (listing.price !== undefined && listing.price !== null) { %>
          &#8377;<%= listing.price.toLocaleString("en-IN") %>/night
        <% } else { %>
          Price not available
        <% } %>
      </p>
      <p class="card-text"><b><%= listing.location %></b></p>
      <p class="card-text"><b><%= listing.country %></b></p>
    </div>
  </div>
  <br>
  <% if(currUser && currUser._id.equals(listing.owner._id)) { %>
    <a href="/listings/<%= listing._id %>/edit" id="Editlink">Edit</a>
    <form method="POST" action="/listings/<%= listing._id %>?_method=DELETE">
      <button id="Deletelink">Delete</button>
    </form>
  <% } %>
  
  <div class="col-8 offset-3">
    <hr>
    <br>
    
    <% if(currUser) { %>
      <h4>Leave a Review</h4>
      <form action="/listings/<%= listing._id %>/reviews" method="POST" novalidate class="needs-validation">
        <div class="mb-3 mt-3">
          <legend>rating:</legend>
        <fieldset class="starability-coinFlip">
          <input type="radio" id="no-rate" class="input-no-rate" name="review[rating]" value="1" checked aria-label="No rating." />
          <input type="radio" id="first-rate1" name="review[rating]" value="1" />
          <label for="first-rate1" title="Terrible">1 star</label>
          <input type="radio" id="first-rate2" name="review[rating]" value="2" />
          <label for="first-rate2" title="Not good">2 stars</label>
          <input type="radio" id="first-rate3" name="review[rating]" value="3" />
          <label for="first-rate3" title="Average">3 stars</label>
          <input type="radio" id="first-rate4" name="review[rating]" value="4" />
          <label for="first-rate4" title="Very good">4 stars</label>
          <input type="radio" id="first-rate5" name="review[rating]" value="5" />
          <label for="first-rate5" title="Amazing">5 stars</label>
        </fieldset>
      </div>
        <div class="mb-3 mt-3">
          <label for="comment" class="form-label">Comments</label>
          <textarea name="review[comment]" required id="comment" cols="30" rows="5" class="form-control"></textarea>
          <div class="invalid-feedback">Please add some comment for review!</div>
        </div>
        <br>
        <button class="btn btn-outline-dark">Submit</button>
      </form>
    <% } %>
    
    <br><br><br>
    <h4>All Reviews</h4>
    <div class="row">
      <% for(let review of listing.reviews) { %>
        <div class="card col-5 mb-3 ms-4" id="review">
          <div class="card-body">
            <%if(review.author){%><p>By: <%=review.author.username%></p><%}%>
            <p class="card-text"><%= review.comment %></p>
            <p class="starability-result card-text" data-rating="<%=review.rating %>">
            </p>
            <% if(currUser && currUser._id==(listing.owner._id)) { %>
              <form action="/listings/<%= listing._id %>/reviews/<%= review._id %>?_method=DELETE" method="POST">
                <button class="btn btn-dark">Delete</button>
              </form>
            <% } %>
          </div>
        </div>
      <% } %>
    </div>
  </div>

  <%if(listing.geometry.coordinates.length){%>
  <div class="col-8 offset-3">
    <h3>Where You Will Be</h3>
    <div id="map"></div>

  </div>
  <%}%>
</div>


<script src="/javascript/map.js"></script>