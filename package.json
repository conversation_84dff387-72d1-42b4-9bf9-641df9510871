{"engine": {"node": "20.15.0"}, "name": "airbnb", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@mapbox/mapbox-sdk": "^0.16.0", "cloudinary": "^2.4.0", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "dotenv": "^16.4.5", "ejs": "^3.1.10", "ejs-mate": "^4.0.0", "express": "^4.19.2", "express-session": "^1.18.0", "joi": "^17.13.3", "method-override": "^3.0.0", "mongoose": "^8.5.1", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "session-cookies": "^0.1.7"}}