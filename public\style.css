/* CSS Variables for Theme */
:root {
    --bg-color: #ffffff;
    --text-color: #222222;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --navbar-bg: #ffffff;
    --footer-bg: rgb(187, 187, 187);
    --input-bg: #ffffff;
    --input-border: #ced4da;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --shadow-color: rgba(255, 255, 255, 0.1);
    --navbar-bg: #1a1a1a;
    --footer-bg: #2a2a2a;
    --input-bg: #2a2a2a;
    --input-border: #444444;
}

body{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}
.container{
    flex: 1;
}
/* navbar */
.navbar{
    height: 5rem;
    background-color: var(--navbar-bg);
    transition: background-color 0.3s ease;
}
.fa-compass{
    color:#fe424d;
    font-size: 2rem;

}
.nav-link{
    color: var(--text-color) !important;
    font-weight: bold;
    padding-top: 10px;
    transition: color 0.3s ease;
}
.f-info-links a{
    text-decoration: none;
    color: var(--text-color);
    transition: color 0.3s ease;
}
.f-info-links a:hover{
    text-decoration: underline;
    color: var(--text-color);
    cursor:pointer;
}
.f-info-links{
    width: 100%;
}
.f-info-socials{
    width: 100%;
    font-size: 1.3rem;
    /* margin-right: 1rem; */
}
.f-info-socials i{
    margin-right: 1rem;
    margin-top: 1rem;

}
.f-info{
    text-align: center;
    display: flex;
    height: 8rem;
    flex-wrap: wrap;
    justify-content: center;
    background-color: var(--footer-bg);
    align-items:space-evenly;
    transition: background-color 0.3s ease;
}
/* cards */
.card{
    border:none !important;
    margin-bottom: 2rem;
    background-color: var(--card-bg);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px var(--shadow-color);
}
.card-img-top{
    border-radius: 1rem !important;
    width: 100% !important;
    object-fit:cover !important;
}
.card-body{
    padding: 0;

}
.card-text{
    font-weight: 400!important;
    font-family: cursive!important;
    color: var(--text-color);
    transition: color 0.3s ease;
}
.listing-link{
    text-decoration: none;
}
.listing-link p{
    color: var(--text-color);
    transition: color 0.3s ease;
}
/* card effect */
.card-img-overlay{
    opacity:0;
}
.card-img-overlay:hover{
    opacity: 0.2;
    background-color: white;
}


/* Add Page new */
/* margin for new forms */
.mb-2{
    margin: 1rem;
}
.btn-dark{
    margin-left: 1.5rem;
    margin-bottom: 2rem;
    margin-top: 1rem;
}
#newList{
    color: var(--text-color);
    margin: 2rem;
    font-size: 30px;
    transition: color 0.3s ease;
}
#Add{
    background-color: #fe424d;
    font-weight: 500;
    border:2px solid #fe424d;
}

#Add:hover{
    background-color: white;
    color: #fe424d;
    border:2px solid #fe424d;
    cursor: pointer;
    font-weight: 500;
}


/* Edit page */
#Edit{
    background-color: #fe424d;
    font-weight: 500;
    border:2px solid #fe424d;
}
#heade{
    margin-left: 9rem;
}
#Edit:hover{
    background-color: white;
    color: #fe424d;
    border:2px solid #fe424d;
    cursor: pointer;
    font-weight: 500;
}
/* SHow page */
.show-img{
    height: 30vh;
    /* width: ; */
}
#Editlink{
    background-color: #fe424d;
    border:2px solid rgb(247, 24, 24);
    text-align: center;
    color: white;
    font-weight: 400;
    text-decoration: none;
    font-size: 23px;
    border-radius:5px;

}
#Editlink:hover{
    background-color: white;
    border:2px solid  #fe424d;
    color: #fe424d;
    font-weight: 400;
    cursor: pointer;
}
#Deletelink{
    margin-top: 1rem;
    font-size: 22px;
    padding: 0.5rem;
    border-radius: 10px;
    background-color: black;
    color: white;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-bottom: 1rem;
}
#Deletelink:hover{
    color: black;
    border:1px solid black;
    background-color: white;
    cursor: pointer;
    font-size: 22px;
    padding-left: 1rem;
    padding-right: 1rem;
}
#review{
    border:1px solid var(--border-color) !important;
    background-color: var(--card-bg);
    transition: all 0.3s ease;
}

[data-theme="dark"] #review {
    border-color: var(--border-color) !important;
}
/* Map */
#map {
    height: 400px;
    width: 800px;

    margin-right: 3rem;
    margin-top: 3rem;
    margin-bottom: 3rem;
    border:2px solid var(--border-color);
    border-radius: 10px;
    transition: border-color 0.3s ease;
 }

/* Dark mode form styles */
[data-theme="dark"] .form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus {
    background-color: var(--input-bg);
    border-color: #fe424d;
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(254, 66, 77, 0.25);
}

[data-theme="dark"] .form-label {
    color: var(--text-color);
}

[data-theme="dark"] textarea.form-control {
    background-color: var(--input-bg);
    color: var(--text-color);
}

/* Search functionality styles */
.search-results-info {
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.search-results-info h5 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.search-results-info p {
    color: var(--text-color);
    margin: 0;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--text-color);
}

.no-results i {
    font-size: 4rem;
    color: #fe424d;
    margin-bottom: 1rem;
}
