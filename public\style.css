/* CSS Variables for Consistent Styling */
:root {
    --bg-color: #ffffff;
    --text-color: #222222;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --navbar-bg: #ffffff;
    --footer-bg: rgb(187, 187, 187);
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --button-bg: #f8f9fa;
    --button-hover-bg: #e9ecef;
    --accent-color: #fe424d;
    --muted-text: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
}

body{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s ease;
}

/* Responsive base styles */
* {
    box-sizing: border-box;
}

/* Mobile-first responsive design */
@media (max-width: 576px) {
    body {
        font-size: 14px;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    body {
        font-size: 15px;
    }
}

@media (min-width: 769px) {
    body {
        font-size: 16px;
    }
}
.container{
    flex: 1;
}
/* navbar */
.navbar{
    height: 5rem;
    background-color: var(--navbar-bg);
    transition: background-color 0.3s ease;
}
.fa-compass{
    color:#fe424d;
    font-size: 2rem;

}
.nav-link{
    color: var(--text-color) !important;
    font-weight: bold;
    padding-top: 10px;
    transition: color 0.3s ease;
}
.f-info-links a{
    text-decoration: none;
    color: var(--text-color);
    transition: color 0.3s ease;
}
.f-info-links a:hover{
    text-decoration: underline;
    color: var(--text-color);
    cursor:pointer;
}
.f-info-links{
    width: 100%;
}
.f-info-socials{
    width: 100%;
    font-size: 1.3rem;
    /* margin-right: 1rem; */
}
.f-info-socials i{
    margin-right: 1rem;
    margin-top: 1rem;

}
.f-info{
    text-align: center;
    display: flex;
    height: 8rem;
    flex-wrap: wrap;
    justify-content: center;
    background-color: var(--footer-bg);
    align-items:space-evenly;
    transition: background-color 0.3s ease;
}

/* Responsive footer styles */
@media (max-width: 768px) {
    .f-info {
        height: auto;
        padding: 2rem 1rem;
        flex-direction: column;
    }

    .f-info-links {
        margin-bottom: 1rem;
    }

    .f-info-socials {
        font-size: 1.1rem;
    }

    .f-info-socials i {
        margin: 0.5rem;
    }
}
/* cards */
.card{
    border:none !important;
    margin-bottom: 2rem;
    background-color: var(--card-bg);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px var(--shadow-color);
    transform: translateY(-2px);
}

/* Responsive card styles */
@media (max-width: 576px) {
    .card {
        margin-bottom: 1rem;
    }

    .card-img-top {
        height: 200px !important;
    }

    .card-body {
        padding: 1rem;
    }

    .card-text {
        font-size: 0.9rem;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .card-img-top {
        height: 250px !important;
    }
}

@media (min-width: 769px) {
    .card-img-top {
        height: 20rem !important;
    }
}
.card-img-top{
    border-radius: 1rem !important;
    width: 100% !important;
    object-fit:cover !important;
}
.card-body{
    padding: 0;

}
.card-text{
    font-weight: 400!important;
    font-family: cursive!important;
    color: var(--text-color);
    transition: color 0.3s ease;
}
.listing-link{
    text-decoration: none;
}
.listing-link p{
    color: var(--text-color);
    transition: color 0.3s ease;
}
/* card effect */
.card-img-overlay{
    opacity:0;
    transition: opacity 0.3s ease;
}
.card-img-overlay:hover{
    opacity: 0.2;
    background-color: white;
}

/* Dark theme card overlay */
[data-theme="dark"] .card-img-overlay:hover{
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 0.3;
}


/* Add Page new */
/* margin for new forms */
.mb-2{
    margin: 1rem;
}
.btn-dark{
    margin-left: 1.5rem;
    margin-bottom: 2rem;
    margin-top: 1rem;
}

/* Responsive form styles */
@media (max-width: 768px) {
    .mb-2 {
        margin: 0.5rem;
    }

    .btn-dark {
        margin-left: 0.5rem;
        margin-bottom: 1rem;
        margin-top: 0.5rem;
        width: calc(100% - 1rem);
    }

    #newList {
        margin: 1rem;
        font-size: 24px;
        text-align: center;
    }

    #heade {
        margin-left: 1rem;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 0.5rem;
    }

    #Add, #Edit {
        width: 100%;
        margin: 0.5rem 0;
    }

    #Editlink, #Deletelink {
        width: 100%;
        margin: 0.25rem 0;
        text-align: center;
    }
}
#newList{
    color: var(--text-color);
    margin: 2rem;
    font-size: 30px;
    transition: color 0.3s ease;
}
#Add{
    background-color: #fe424d;
    font-weight: 500;
    border:2px solid #fe424d;
    transition: all 0.3s ease;
}

#Add:hover{
    background-color: white;
    color: #fe424d;
    border:2px solid #fe424d;
    cursor: pointer;
    font-weight: 500;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(254, 66, 77, 0.3);
}

/* Dark theme button styles */
[data-theme="dark"] #Add {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

[data-theme="dark"] #Add:hover {
    background-color: var(--button-hover-bg);
    color: var(--accent-color);
    border-color: var(--accent-color);
}


/* Edit page */
#Edit{
    background-color: #fe424d;
    font-weight: 500;
    border:2px solid #fe424d;
    transition: all 0.3s ease;
}
#heade{
    margin-left: 9rem;
}
#Edit:hover{
    background-color: white;
    color: #fe424d;
    border:2px solid #fe424d;
    cursor: pointer;
    font-weight: 500;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(254, 66, 77, 0.3);
}

/* Dark theme edit button */
[data-theme="dark"] #Edit {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

[data-theme="dark"] #Edit:hover {
    background-color: var(--button-hover-bg);
    color: var(--accent-color);
    border-color: var(--accent-color);
}
/* SHow page */
.show-img{
    height: 30vh;
    /* width: ; */
}
#Editlink{
    background-color: #fe424d;
    border:2px solid rgb(247, 24, 24);
    text-align: center;
    color: white;
    font-weight: 400;
    text-decoration: none;
    font-size: 23px;
    border-radius:5px;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 0.5rem 1rem;
}
#Editlink:hover{
    background-color: white;
    border:2px solid  #fe424d;
    color: #fe424d;
    font-weight: 400;
    cursor: pointer;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(254, 66, 77, 0.3);
}
#Deletelink{
    margin-top: 1rem;
    font-size: 22px;
    padding: 0.5rem;
    border-radius: 10px;
    background-color: black;
    color: white;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border: 1px solid black;
}
#Deletelink:hover{
    color: black;
    border:1px solid black;
    background-color: white;
    cursor: pointer;
    font-size: 22px;
    padding-left: 1rem;
    padding-right: 1rem;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Dark theme show page buttons */
[data-theme="dark"] #Editlink {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

[data-theme="dark"] #Editlink:hover {
    background-color: var(--button-hover-bg);
    color: var(--accent-color);
    border-color: var(--accent-color);
}

[data-theme="dark"] #Deletelink {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

[data-theme="dark"] #Deletelink:hover {
    background-color: var(--button-hover-bg);
    color: var(--danger-color);
    border-color: var(--danger-color);
}
#review{
    border:1px solid var(--border-color) !important;
    background-color: var(--card-bg);
    transition: all 0.3s ease;
}

[data-theme="dark"] #review {
    border-color: var(--border-color) !important;
}
/* Map */
#map {
    height: 400px;
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    transition: border-color 0.3s ease;
}

/* Responsive map styles */
@media (max-width: 768px) {
    #map {
        height: 300px;
        margin: 1rem auto;
    }
}

@media (max-width: 576px) {
    #map {
        height: 250px;
        margin: 0.5rem auto;
    }
}

/* Dark mode form styles */
[data-theme="dark"] .form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus {
    background-color: var(--input-bg);
    border-color: var(--accent-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(254, 66, 77, 0.25);
}

[data-theme="dark"] .form-control::placeholder {
    color: var(--muted-text);
}

[data-theme="dark"] .form-label {
    color: var(--text-color);
}

[data-theme="dark"] textarea.form-control {
    background-color: var(--input-bg);
    color: var(--text-color);
}

/* Dark theme Bootstrap components */
[data-theme="dark"] .btn-dark {
    background-color: var(--button-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-dark:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--accent-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-outline-secondary {
    color: var(--text-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--accent-color);
    color: var(--text-color);
}

/* Dark theme alerts */
[data-theme="dark"] .alert-success {
    background-color: rgba(35, 134, 54, 0.2);
    border-color: var(--success-color);
    color: #7dd87f;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(218, 54, 51, 0.2);
    border-color: var(--danger-color);
    color: #ff7b7b;
}

/* Dark theme modal and dropdown */
[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
    border-top-color: var(--border-color);
}

[data-theme="dark"] .dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--button-hover-bg);
    color: var(--text-color);
}

/* Search functionality styles */
.search-results-info {
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.search-results-info h5 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.search-results-info p {
    color: var(--text-color);
    margin: 0;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--text-color);
}

.no-results i {
    font-size: 4rem;
    color: #fe424d;
    margin-bottom: 1rem;
}

/* Enhanced dark theme styles */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: var(--text-color);
}

[data-theme="dark"] p {
    color: var(--text-color);
}

[data-theme="dark"] .container {
    color: var(--text-color);
}

/* Dark theme for rating stars */
[data-theme="dark"] .starability-result {
    color: #ffd700;
}

/* Dark theme for links */
[data-theme="dark"] a {
    color: var(--accent-color);
}

[data-theme="dark"] a:hover {
    color: #e63946;
}

/* Dark theme for borders and dividers */
[data-theme="dark"] hr {
    border-color: var(--border-color);
}

/* Dark theme for table elements if any */
[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table-dark {
    background-color: var(--card-bg);
}

/* Dark theme for badges */
[data-theme="dark"] .badge {
    background-color: var(--button-bg);
    color: var(--text-color);
}

/* Dark theme for pagination */
[data-theme="dark"] .page-link {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .page-link:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--accent-color);
    color: var(--text-color);
}

[data-theme="dark"] .page-item.active .page-link {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Smooth scrollbar for dark theme */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--button-hover-bg);
}

/* Dark theme for selection */
[data-theme="dark"] ::selection {
    background-color: rgba(254, 66, 77, 0.3);
    color: var(--text-color);
}
