<% layout("/layouts/boilerplate") %>
    <h3 id="heade">Edit your Listing</h3>
    <form action="/listings/<%=listing._id%>?_method=PUT" method="POST" class="needs-validation" novalidate enctype="multipart/form-data">
    <div class="row mt-3">
        <div class="col-8 offset-2">
            <div class="mb-3">
                <label for="title" class="form-label">Title</label>
                <input type="text" value="<%=listing.title%>" class="form-control"name="listing[title]" required>
                <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea name="listing[description]" class="form-control" required><%=listing.description%></textarea>
                <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div>
            </div>
            <div class="mb-3">
                <h3>Original Listing Image</h3>
                <br>
                <img src="<%=originalImageUrl%>" alt="">
            </div>
            <div class="mb-3">
                <label for="image" class="form-label">Upload Image</label>
                <input  type="file" name="listing[image][url]" class="form-control" required>
                <!-- <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div> -->
            </div>
           <div class="row">
            <div class="mb-3 col-md-4">
                    <label for="price" class="form-label">Price</label>
                    <input name="listing[price]" value="<%=listing.price%>" type="number" class="form-control" required>
                    <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div>
            
            </div>
            <div class="mb-3 col-md-8">
                <label for="location" class="form-label">Location</label>
                <input name="listing[location]" class="form-control" value="<%=listing.location%>" type="text" required>
                <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div>
            </div>
           </div>
            <div class="mb-3">
                <label for="country" class="form-label">Country</label>
                <input type="text" value="<%=listing.country%>" name="listing[country]" class="form-control" required>
                <div class="valid-feedback">Looks Good!</div>
                <div class="invalid-feedback">Edit carefully ⚠</div>
            </div>
        <button class="btn btn-dark mt-2" id="Edit">Edit</button>
        </div>

       
    </div>
</form>
<script src="/javascript/script.js"></script>