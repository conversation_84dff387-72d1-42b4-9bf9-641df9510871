// Example starter JavaScript for disabling form submissions if there are invalid fields
(function () {
    'use strict'

    // Fetch all the forms we want to apply custom Bootstrap validation styles to
    var forms = document.querySelectorAll('.needs-validation')

    // Loop over them and prevent submission
    Array.prototype.slice.call(forms)
      .forEach(function (form) {
        form.addEventListener('submit', function (event) {
          if (!form.checkValidity()) {
            event.preventDefault()
            event.stopPropagation()
          }

          form.classList.add('was-validated')
        }, false)
      })
  })();



  // $('.alert').alert();

// Theme Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');
    const body = document.body;

    // Check for saved theme preference or default to 'light'
    const currentTheme = localStorage.getItem('theme') || 'light';

    // Apply the saved theme
    if (currentTheme === 'dark') {
        body.setAttribute('data-theme', 'dark');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }

    // Theme toggle event listener
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const currentTheme = body.getAttribute('data-theme');

            // Add a subtle rotation animation
            themeIcon.style.transform = 'rotate(180deg)';

            setTimeout(() => {
                if (currentTheme === 'dark') {
                    // Switch to light mode
                    body.removeAttribute('data-theme');
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                    localStorage.setItem('theme', 'light');
                } else {
                    // Switch to dark mode
                    body.setAttribute('data-theme', 'dark');
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    localStorage.setItem('theme', 'dark');
                }

                // Reset rotation
                setTimeout(() => {
                    themeIcon.style.transform = 'rotate(0deg)';
                }, 150);
            }, 150);
        });
    }
});

// Search functionality enhancements
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('input');
    const searchForm = document.querySelector('form[action="/listings"]');

    if (searchInput && searchForm) {
        // Add search suggestions or auto-complete functionality here if needed
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });

        // Clear search functionality
        if (searchInput.value) {
            const clearButton = document.createElement('button');
            clearButton.type = 'button';
            clearButton.className = 'btn btn-outline-secondary';
            clearButton.innerHTML = '<i class="fas fa-times"></i>';
            clearButton.style.marginLeft = '5px';
            clearButton.onclick = function() {
                searchInput.value = '';
                window.location.href = '/listings';
            };
            searchInput.parentNode.appendChild(clearButton);
        }
    }
});